import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TaobaoOutlinedSvg from "@ant-design/icons-svg/es/asn/TaobaoOutlined";
import AntdIcon from "../components/AntdIcon";
var TaobaoOutlined = function TaobaoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TaobaoOutlinedSvg
  }));
};

/**![taobao](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2OC41IDI3My43YTY4LjcgNjguNyAwIDEwMTM3LjQgMCA2OC43IDY4LjcgMCAxMC0xMzcuNCAwem03MzAgNzkuMnMtMjMuNy0xODQuNC00MjYuOS03MC4xYzE3LjMtMzAgMjUuNi00OS41IDI1LjYtNDkuNUwzOTYuNCAyMDVzLTQwLjYgMTMyLjYtMTEzIDE5NC40YzAgMCA3MC4xIDQwLjYgNjkuNCAzOS40IDIwLjEtMjAuMSAzOC4yLTQwLjYgNTMuNy02MC40IDE2LjEtNyAzMS41LTEzLjYgNDYuNy0xOS44LTE4LjYgMzMuNS00OC43IDgzLjgtNzguOCAxMTUuNmw0Mi40IDM3czI4LjgtMjcuNyA2MC40LTYxLjJoMzZ2NjEuOEgzNzIuOXY0OS41aDE0MC4zdjExOC41Yy0xLjcgMC0zLjYgMC01LjQtLjItMTUuNC0uNy0zOS41LTMuMy00OS0xOC4yLTExLjUtMTguMS0zLTUxLjUtMi40LTcxLjloLTk3bC0zLjQgMS44cy0zNS41IDE1OS4xIDEwMi4zIDE1NS41YzEyOS4xIDMuNiAyMDMtMzYgMjM4LjYtNjMuMWwxNC4yIDUyLjYgNzkuNi0zMy4yLTUzLjktMTMxLjktNjQuNiAyMC4xIDEyLjEgNDUuMmMtMTYuNiAxMi40LTM1LjYgMjEuNy01Ni4yIDI4LjRWNTYxLjNoMTM3LjF2LTQ5LjVINjI4LjFWNDUwaDEzNy42di00OS41SDUyMS4zYzE3LjYtMjEuNCAzMS41LTQxLjEgMzUtNTMuNmwtNDIuNS0xMS42YzE4Mi44LTY1LjUgMjg0LjUtNTQuMiAyODMuNiA1My4ydjI4Mi44czEwLjggOTcuMS0xMDAuNCA5MC4xbC02MC4yLTEyLjktMTQuMiA1Ny4xUzg4Mi41IDg4MCA5MDMuNyA2ODAuMmMyMS4zLTIwMC01LjItMzI3LjMtNS4yLTMyNy4zem0tNzA3LjQgMTguM2wtNDUuNCA2OS43IDgzLjYgNTIuMXM1NiAyOC41IDI5LjQgODEuOUMyMzMuOCA2MjUuNSAxMTIgNzM2LjMgMTEyIDczNi4zbDEwOSA2OC4xYzc1LjQtMTYzLjcgNzAuNS0xNDIgODkuNS0yMDAuNyAxOS41LTYwLjEgMjMuNy0xMDUuOS05LjQtMTM5LjEtNDIuNC00Mi42LTQ3LTQ2LjYtMTEwLTkzLjR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(TaobaoOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TaobaoOutlined';
}
export default RefIcon;