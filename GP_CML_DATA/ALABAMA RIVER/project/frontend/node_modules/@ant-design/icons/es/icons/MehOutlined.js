import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MehOutlinedSvg from "@ant-design/icons-svg/es/asn/MehOutlined";
import AntdIcon from "../components/AntdIcon";
var MehOutlined = function MehOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MehOutlinedSvg
  }));
};

/**![meh](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4OCA0MjFhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem0zNTIgMGE0OCA0OCAwIDEwOTYgMCA0OCA0OCAwIDEwLTk2IDB6TTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNjMgNzExYy0zNC4yIDM0LjItNzQgNjEtMTE4LjMgNzkuOEM2MTEgODc0LjIgNTYyLjMgODg0IDUxMiA4ODRjLTUwLjMgMC05OS05LjgtMTQ0LjgtMjkuMkEzNzAuNCAzNzAuNCAwIDAxMjQ4LjkgNzc1Yy0zNC4yLTM0LjItNjEtNzQtNzkuOC0xMTguM0MxNDkuOCA2MTEgMTQwIDU2Mi4zIDE0MCA1MTJzOS44LTk5IDI5LjItMTQ0LjhBMzcwLjQgMzcwLjQgMCAwMTI0OSAyNDguOWMzNC4yLTM0LjIgNzQtNjEgMTE4LjMtNzkuOEM0MTMgMTQ5LjggNDYxLjcgMTQwIDUxMiAxNDBjNTAuMyAwIDk5IDkuOCAxNDQuOCAyOS4yQTM3MC40IDM3MC40IDAgMDE3NzUuMSAyNDljMzQuMiAzNC4yIDYxIDc0IDc5LjggMTE4LjNDODc0LjIgNDEzIDg4NCA0NjEuNyA4ODQgNTEycy05LjggOTktMjkuMiAxNDQuOEEzNjguODkgMzY4Ljg5IDAgMDE3NzUgNzc1ek02NjQgNTY1SDM2MGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgzMDRjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MehOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MehOutlined';
}
export default RefIcon;