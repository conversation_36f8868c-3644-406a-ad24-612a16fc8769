import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TrophyOutlinedSvg from "@ant-design/icons-svg/es/asn/TrophyOutlined";
import AntdIcon from "../components/AntdIcon";
var TrophyOutlined = function TrophyOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TrophyOutlinedSvg
  }));
};

/**![trophy](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2OCAxNjBoLTkydi00MGMwLTQuNC0zLjYtOC04LThIMjU2Yy00LjQgMC04IDMuNi04IDh2NDBoLTkyYTQ0IDQ0IDAgMDAtNDQgNDR2MTQ4YzAgODEuNyA2MCAxNDkuNiAxMzguMiAxNjJDMjY1LjcgNjMwLjIgMzU5IDcyMS43IDQ3NiA3MzQuNXYxMDUuMkgyODBjLTE3LjcgMC0zMiAxNC4zLTMyIDMyVjkwNGMwIDQuNCAzLjYgOCA4IDhoNTEyYzQuNCAwIDgtMy42IDgtOHYtMzIuM2MwLTE3LjctMTQuMy0zMi0zMi0zMkg1NDhWNzM0LjVDNjY1IDcyMS43IDc1OC4zIDYzMC4yIDc3My44IDUxNCA4NTIgNTAxLjYgOTEyIDQzMy43IDkxMiAzNTJWMjA0YTQ0IDQ0IDAgMDAtNDQtNDR6TTE4NCAzNTJWMjMyaDY0djIwNy42YTkxLjk5IDkxLjk5IDAgMDEtNjQtODcuNnptNTIwIDEyOGMwIDQ5LjEtMTkuMSA5NS40LTUzLjkgMTMwLjEtMzQuOCAzNC44LTgxIDUzLjktMTMwLjEgNTMuOWgtMTZjLTQ5LjEgMC05NS40LTE5LjEtMTMwLjEtNTMuOS0zNC44LTM0LjgtNTMuOS04MS01My45LTEzMC4xVjE4NGgzODR2Mjk2em0xMzYtMTI4YzAgNDEtMjYuOSA3NS44LTY0IDg3LjZWMjMyaDY0djEyMHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(TrophyOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TrophyOutlined';
}
export default RefIcon;