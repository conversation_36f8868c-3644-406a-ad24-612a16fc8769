import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import GoogleOutlinedSvg from "@ant-design/icons-svg/es/asn/GoogleOutlined";
import AntdIcon from "../components/AntdIcon";
var GoogleOutlined = function GoogleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: GoogleOutlinedSvg
  }));
};

/**![google](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MSA0NDIuNEg1MTkuN3YxNDguNWgyMDYuNGMtOC45IDQ4LTM1LjkgODguNi03Ni42IDExNS44LTM0LjQgMjMtNzguMyAzNi42LTEyOS45IDM2LjYtOTkuOSAwLTE4NC40LTY3LjUtMjE0LjYtMTU4LjItNy42LTIzLTEyLTQ3LjYtMTItNzIuOXM0LjQtNDkuOSAxMi03Mi45YzMwLjMtOTAuNiAxMTQuOC0xNTguMSAyMTQuNy0xNTguMSA1Ni4zIDAgMTA2LjggMTkuNCAxNDYuNiA1Ny40bDExMC0xMTAuMWMtNjYuNS02Mi0xNTMuMi0xMDAtMjU2LjYtMTAwLTE0OS45IDAtMjc5LjYgODYtMzQyLjcgMjExLjQtMjYgNTEuOC00MC44IDExMC40LTQwLjggMTcyLjRTMTUxIDYzMi44IDE3NyA2ODQuNkMyNDAuMSA4MTAgMzY5LjggODk2IDUxOS43IDg5NmMxMDMuNiAwIDE5MC40LTM0LjQgMjUzLjgtOTMgNzIuNS02Ni44IDExNC40LTE2NS4yIDExNC40LTI4Mi4xIDAtMjcuMi0yLjQtNTMuMy02LjktNzguNXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(GoogleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GoogleOutlined';
}
export default RefIcon;