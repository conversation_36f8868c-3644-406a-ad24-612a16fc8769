import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TrophyFilledSvg from "@ant-design/icons-svg/es/asn/TrophyFilled";
import AntdIcon from "../components/AntdIcon";
var TrophyFilled = function TrophyFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TrophyFilledSvg
  }));
};

/**![trophy](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2OCAxNjBoLTkydi00MGMwLTQuNC0zLjYtOC04LThIMjU2Yy00LjQgMC04IDMuNi04IDh2NDBoLTkyYTQ0IDQ0IDAgMDAtNDQgNDR2MTQ4YzAgODEuNyA2MCAxNDkuNiAxMzguMiAxNjJDMjY1LjYgNjMwLjIgMzU5IDcyMS44IDQ3NiA3MzQuNXYxMDUuMkgyODBjLTE3LjcgMC0zMiAxNC4zLTMyIDMyVjkwNGMwIDQuNCAzLjYgOCA4IDhoNTEyYzQuNCAwIDgtMy42IDgtOHYtMzIuM2MwLTE3LjctMTQuMy0zMi0zMi0zMkg1NDhWNzM0LjVDNjY1IDcyMS44IDc1OC40IDYzMC4yIDc3My44IDUxNCA4NTIgNTAxLjYgOTEyIDQzMy43IDkxMiAzNTJWMjA0YTQ0IDQ0IDAgMDAtNDQtNDR6TTI0OCA0MzkuNmMtMzcuMS0xMS45LTY0LTQ2LjctNjQtODcuNlYyMzJoNjR2MjA3LjZ6TTg0MCAzNTJjMCA0MS0yNi45IDc1LjgtNjQgODcuNlYyMzJoNjR2MTIweiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(TrophyFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TrophyFilled';
}
export default RefIcon;