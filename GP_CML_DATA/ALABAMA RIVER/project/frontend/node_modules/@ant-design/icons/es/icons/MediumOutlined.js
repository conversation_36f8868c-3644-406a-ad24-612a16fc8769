import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MediumOutlinedSvg from "@ant-design/icons-svg/es/asn/MediumOutlined";
import AntdIcon from "../components/AntdIcon";
var MediumOutlined = function MediumOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MediumOutlinedSvg
  }));
};

/**![medium](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzNC43IDI3OS44bDYxLjMtNTguOVYyMDhINjgzLjdMNTMyLjQgNTg2LjQgMzYwLjMgMjA4SDEzNy43djEyLjlsNzEuNiA4Ni42YzcgNi40IDEwLjYgMTUuOCA5LjcgMjUuMlY2NzNjMi4yIDEyLjMtMS43IDI0LjgtMTAuMyAzMy43TDEyOCA4MDV2MTIuN2gyMjguNnYtMTIuOWwtODAuNi05OGEzOS45OSAzOS45OSAwIDAxLTExLjEtMzMuN1YzNzguN2wyMDAuNyA0MzkuMmgyMy4zbDE3Mi42LTQzOS4ydjM0OS45YzAgOS4yIDAgMTEuMS02IDE3LjJsLTYyLjEgNjAuM1Y4MTloMzAxLjJ2LTEyLjlsLTU5LjktNTguOWMtNS4yLTQtNy45LTEwLjctNi44LTE3LjJWMjk3YTE4LjEgMTguMSAwIDAxNi44LTE3LjJ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MediumOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MediumOutlined';
}
export default RefIcon;