import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import IeOutlinedSvg from "@ant-design/icons-svg/es/asn/IeOutlined";
import AntdIcon from "../components/AntdIcon";
var IeOutlined = function IeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: IeOutlinedSvg
  }));
};

/**![ie](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1Mi42IDM2Ny42YzE2LjMtMzYuOSAzMi4xLTkwLjcgMzIuMS0xMzEuOCAwLTEwOS4xLTExOS41LTE0Ny42LTMxNC41LTU3LjktMTYxLjQtMTAuOC0zMTYuOCAxMTAuNS0zNTUuNiAyNzkuNyA0Ni4zLTUyLjMgMTE3LjQtMTIzLjQgMTgzLTE1MS43QzMxNi4xIDM3OC4zIDI0Ni43IDQ3MCAxOTQgNTY1LjZjLTMxLjEgNTYuOS02NiAxNDguOC02NiAyMTcuNSAwIDE0Ny45IDEzOS4zIDEyOS44IDI3MC40IDYzIDQ3LjEgMjMuMSA5OS44IDIzLjQgMTUyLjUgMjMuNCAxNDUuNyAwIDI3Ni40LTgxLjQgMzI1LjItMjE5SDY5NC45Yy03OC44IDEzMi45LTI5NS4yIDc5LjUtMjk1LjItNzEuMmg0OTMuMmM5LjYtNjUuNC0yLjUtMTQzLjYtNDAuMy0yMTEuN3pNMjI0LjggNjQ4LjNjMjYuNiA3Ni43IDgwLjYgMTQzLjggMTUwLjQgMTg1LTEzMy4xIDczLjQtMjU5LjkgNDMuNi0xNTAuNC0xODV6bTE3NC0xNjMuM2MzLTgyLjcgNzUuNC0xNDIuMyAxNTYtMTQyLjMgODAuMSAwIDE1MyA1OS42IDE1NiAxNDIuM2gtMzEyem0yNzYuOC0yODEuNGMzMi4xLTE1LjQgNzIuOC0zMyAxMDguOC0zMyA0Ny4xIDAgODEuNCAzMi42IDgxLjQgODAuNiAwIDMwLTExLjEgNzMuNS0yMS45IDEwMS44LTM5LjMtNjMuNS05OC45LTEyMi40LTE2OC4zLTE0OS40eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(IeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'IeOutlined';
}
export default RefIcon;