import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MergeFilledSvg from "@ant-design/icons-svg/es/asn/MergeFilled";
import AntdIcon from "../components/AntdIcon";
var MergeFilled = function MergeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MergeFilledSvg
  }));
};

/**![merge](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMjg0IDkyNGM2MS44NiAwIDExMi01MC4xNCAxMTItMTEyIDAtNDkuMjYtMzEuOC05MS4xLTc2LTEwNi4wOVY0MjEuNjNsMzg2LjQ5IDEyNi41NS4wMSA5NS45MkM2NjEgNjU4LjM0IDYyOCA3MDAuOCA2MjggNzUxYzAgNjEuODYgNTAuMTQgMTEyIDExMiAxMTJzMTEyLTUwLjE0IDExMi0xMTJjMC00OC4zMy0zMC42LTg5LjUtNzMuNS0xMDUuMmwtLjAxLTExMy4wNGE1MC43MyA1MC43MyAwIDAwLTM0Ljk1LTQ4LjJMMzIwIDM0NS44NVYzMTguMWM0My42NC0xNC44IDc1LjItNTUuNzggNzUuOTktMTA0LjI0TDM5NiAyMTJjMC02MS44Ni01MC4xNC0xMTItMTEyLTExMnMtMTEyIDUwLjE0LTExMiAxMTJjMCA0OS4yNiAzMS44IDkxLjEgNzYgMTA2LjA5VjcwNS45Yy00NC4yIDE1LTc2IDU2LjgzLTc2IDEwNi4wOSAwIDYxLjg2IDUwLjE0IDExMiAxMTIgMTEyIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MergeFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MergeFilled';
}
export default RefIcon;