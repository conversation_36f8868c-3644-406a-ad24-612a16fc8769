import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import StepBackwardOutlinedSvg from "@ant-design/icons-svg/es/asn/StepBackwardOutlined";
import AntdIcon from "../components/AntdIcon";
var StepBackwardOutlined = function StepBackwardOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: StepBackwardOutlinedSvg
  }));
};

/**![step-backward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0Ny42IDUyOC45NWwzODMuMiAzMDEuMDJjMTQuMjUgMTEuMiAzNS4yIDEuMSAzNS4yLTE2Ljk1VjIxMC45N2MwLTE4LjA1LTIwLjk1LTI4LjE0LTM1LjItMTYuOTRMMzQ3LjYgNDk1LjA1YTIxLjUzIDIxLjUzIDAgMDAwIDMzLjlNMzMwIDg2NGgtNjRhOCA4IDAgMDEtOC04VjE2OGE4IDggMCAwMTgtOGg2NGE4IDggMCAwMTggOHY2ODhhOCA4IDAgMDEtOCA4IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(StepBackwardOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StepBackwardOutlined';
}
export default RefIcon;