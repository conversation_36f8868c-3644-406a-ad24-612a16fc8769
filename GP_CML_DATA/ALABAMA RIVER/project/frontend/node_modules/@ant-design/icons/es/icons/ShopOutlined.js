import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ShopOutlinedSvg from "@ant-design/icons-svg/es/asn/ShopOutlined";
import AntdIcon from "../components/AntdIcon";
var ShopOutlined = function ShopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ShopOutlinedSvg
  }));
};

/**![shop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MiAyNzIuMVYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMTc0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMjguMWMtMTYuNyAxLTMwIDE0LjktMzAgMzEuOXYxMzEuN2ExNzcgMTc3IDAgMDAxNC40IDcwLjRjNC4zIDEwLjIgOS42IDE5LjggMTUuNiAyOC45djM0NWMwIDE3LjYgMTQuMyAzMiAzMiAzMmg2NzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWNTM1YTE3NSAxNzUgMCAwMDE1LjYtMjguOWM5LjUtMjIuMyAxNC40LTQ2IDE0LjQtNzAuNFYzMDRjMC0xNy0xMy4zLTMwLjktMzAtMzEuOXpNMjE0IDE4NGg1OTZ2ODhIMjE0di04OHptMzYyIDY1Ni4xSDQ0OFY3MzZoMTI4djEwNC4xem0yMzQgMEg2NDBWNzA0YzAtMTcuNy0xNC4zLTMyLTMyLTMySDQxNmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MTM2LjFIMjE0VjU5Ny45YzIuOSAxLjQgNS45IDIuOCA5IDQgMjIuMyA5LjQgNDYgMTQuMSA3MC40IDE0LjFzNDgtNC43IDcwLjQtMTQuMWMxMy44LTUuOCAyNi44LTEzLjIgMzguNy0yMi4xLjItLjEuNC0uMS42IDBhMTgwLjQgMTgwLjQgMCAwMDM4LjcgMjIuMWMyMi4zIDkuNCA0NiAxNC4xIDcwLjQgMTQuMSAyNC40IDAgNDgtNC43IDcwLjQtMTQuMSAxMy44LTUuOCAyNi44LTEzLjIgMzguNy0yMi4xLjItLjEuNC0uMS42IDBhMTgwLjQgMTgwLjQgMCAwMDM4LjcgMjIuMWMyMi4zIDkuNCA0NiAxNC4xIDcwLjQgMTQuMSAyNC40IDAgNDgtNC43IDcwLjQtMTQuMSAzLTEuMyA2LTIuNiA5LTR2MjQyLjJ6bTMwLTQwNC40YzAgNTkuOC00OSAxMDguMy0xMDkuMyAxMDguMy00MC44IDAtNzYuNC0yMi4xLTk1LjItNTQuOS0yLjktNS04LjEtOC4xLTEzLjktOC4xaC0uNmMtNS43IDAtMTEgMy4xLTEzLjkgOC4xQTEwOS4yNCAxMDkuMjQgMCAwMTUxMiA1NDRjLTQwLjcgMC03Ni4yLTIyLTk1LTU0LjctMy01LjEtOC40LTguMy0xNC4zLTguM3MtMTEuNCAzLjItMTQuMyA4LjNhMTA5LjYzIDEwOS42MyAwIDAxLTk1LjEgNTQuN0MyMzMgNTQ0IDE4NCA0OTUuNSAxODQgNDM1Ljd2LTkxLjJjMC0uMy4yLS41LjUtLjVoNjU1Yy4zIDAgLjUuMi41LjV2OTEuMnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(ShopOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShopOutlined';
}
export default RefIcon;