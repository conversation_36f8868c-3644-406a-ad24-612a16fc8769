import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MinusSquareFilledSvg from "@ant-design/icons-svg/es/asn/MinusSquareFilled";
import AntdIcon from "../components/AntdIcon";
var MinusSquareFilled = function MinusSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MinusSquareFilledSvg
  }));
};

/**![minus-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzA0IDUzNmMwIDQuNC0zLjYgOC04IDhIMzI4Yy00LjQgMC04LTMuNi04LTh2LTQ4YzAtNC40IDMuNi04IDgtOGgzNjhjNC40IDAgOCAzLjYgOCA4djQ4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(MinusSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MinusSquareFilled';
}
export default RefIcon;