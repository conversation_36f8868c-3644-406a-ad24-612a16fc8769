import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SketchCircleFilledSvg from "@ant-design/icons-svg/es/asn/SketchCircleFilled";
import AntdIcon from "../components/AntdIcon";
var SketchCircleFilled = function SketchCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SketchCircleFilledSvg
  }));
};

/**![sketch-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU4Mi4zIDYyNS42bDE0Ny45LTE2Ni4zaC02My40em05MC0yMDIuM2g2Mi41bC05Mi4xLTExNS4xem0tMjc0LjcgMzZMNTEyIDY4NC41bDExNC40LTIyNS4yek01MTIgNjRDMjY0LjYgNjQgNjQgMjY0LjYgNjQgNTEyczIwMC42IDQ0OCA0NDggNDQ4IDQ0OC0yMDAuNiA0NDgtNDQ4Uzc1OS40IDY0IDUxMiA2NHptMjg2LjcgMzgwLjJMNTE1LjggNzYyLjNjLTEgMS4xLTIuNCAxLjctMy44IDEuN3MtMi44LS42LTMuOC0xLjdMMjI1LjMgNDQ0LjJhNS4xNCA1LjE0IDAgMDEtLjItNi42TDM2NS42IDI2MmMxLTEuMiAyLjQtMS45IDQtMS45aDI4NC42YzEuNiAwIDMgLjcgNCAxLjlsMTQwLjUgMTc1LjZhNC45IDQuOSAwIDAxMCA2LjZ6bS0xOTAuNS0yMC45TDUxMiAzMjYuMWwtOTYuMiA5Ny4yek00MjAuMyAzMDEuMWwtMjMuMSA4OS44IDg4LjgtODkuOHptMTgzLjQgMEg1MzhsODguOCA4OS44em0tMjIyLjQgNy4xbC05Mi4xIDExNS4xaDYyLjV6bS04Ny41IDE1MS4xbDE0Ny45IDE2Ni4zLTg0LjUtMTY2LjN6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(SketchCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SketchCircleFilled';
}
export default RefIcon;