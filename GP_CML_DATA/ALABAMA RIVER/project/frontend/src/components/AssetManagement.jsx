import React, { useEffect, useMemo, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Classes, FocusStyleManager, Navbar, Divider, H4, Tag, ProgressBar, Button, Menu, MenuItem, Popover, Position, Spinner } from '@blueprintjs/core';
import { fetchAssets, updateAssetReady, updateCmlReady, fetchPlants, setPlantName } from '../redux/assetsSlice';
import AssetSidebar from './AssetSidebar';
import AssetViewer from './AssetViewer';

function AssetManagement() {
  const dispatch = useDispatch();
  const { plantName, assets, selectedAsset, plants, plantsLoading, plantsError } = useSelector(state => state.assets);

  useEffect(() => {
    // Fetch the list of plants when the component mounts
    dispatch(fetchPlants());
  }, [dispatch]);
  
  useEffect(() => {
    dispatch(fetchAssets(plantName));
  }, [dispatch, plantName]);
  
  // Handle plant selection
  const handlePlantChange = (newPlantName) => {
    if (newPlantName !== plantName) {
      dispatch(setPlantName(newPlantName));
    }
  };
  
  // Navigate to the previous asset in the list
  const navigateToPreviousAsset = useCallback(() => {
    console.log('Navigating to previous asset');
    console.log('Selected asset:', selectedAsset?.Tag_Name);
    console.log('Total assets:', assets.length);
    
    // If no asset is selected but we have assets, select the last one
    if (!selectedAsset && assets.length > 0) {
      console.log('No asset selected, selecting the last one');
      dispatch(selectAsset(assets[assets.length - 1]));
      return;
    }
    
    if (!selectedAsset || assets.length === 0) {
      console.log('No asset selected or no assets available');
      return;
    }
    
    // Get a flat list of all assets across all templates
    const sortedAssets = [...assets].sort((a, b) => a.Tag_Name.localeCompare(b.Tag_Name));
    const currentIndex = sortedAssets.findIndex(asset => asset.Tag_Name === selectedAsset.Tag_Name);
    
    console.log('Current index:', currentIndex);
    
    if (currentIndex > 0) {
      console.log('Selecting previous asset:', sortedAssets[currentIndex - 1].Tag_Name);
      // Navigate to the previous asset
      dispatch(selectAsset(sortedAssets[currentIndex - 1]));
    } else {
      console.log('Already at the first asset');
    }
  }, [assets, selectedAsset, dispatch]);
  
  // Navigate to the next asset in the list
  const navigateToNextAsset = useCallback(() => {
    console.log('Navigating to next asset');
    console.log('Selected asset:', selectedAsset?.Tag_Name);
    console.log('Total assets:', assets.length);
    
    // If no asset is selected but we have assets, select the first one
    if (!selectedAsset && assets.length > 0) {
      console.log('No asset selected, selecting the first one');
      dispatch(selectAsset(assets[0]));
      return;
    }
    
    if (!selectedAsset || assets.length === 0) {
      console.log('No asset selected or no assets available');
      return;
    }
    
    // Get a flat list of all assets across all templates
    const sortedAssets = [...assets].sort((a, b) => a.Tag_Name.localeCompare(b.Tag_Name));
    const currentIndex = sortedAssets.findIndex(asset => asset.Tag_Name === selectedAsset.Tag_Name);
    
    console.log('Current index:', currentIndex);
    
    if (currentIndex < sortedAssets.length - 1) {
      console.log('Selecting next asset:', sortedAssets[currentIndex + 1].Tag_Name);
      // Navigate to the next asset
      dispatch(selectAsset(sortedAssets[currentIndex + 1]));
    } else {
      console.log('Already at the last asset');
    }
  }, [assets, selectedAsset, dispatch]);

  // Create a ref to store the navigation functions
  const navigationFunctionsRef = useRef({ navigateToPreviousAsset, navigateToNextAsset });
  
  // Update the ref when the functions change
  useEffect(() => {
    navigationFunctionsRef.current = { navigateToPreviousAsset, navigateToNextAsset };
  }, [navigateToPreviousAsset, navigateToNextAsset]);
  
  // Set up keyboard event listeners for navigation and shortcuts
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+Q: Previous asset
      if (event.ctrlKey && event.key.toLowerCase() === 'q') {
        console.log('Ctrl+Q pressed - Navigate to previous asset');
        event.preventDefault();
        navigationFunctionsRef.current.navigateToPreviousAsset();
      }
      
      // Ctrl+W: Next asset
      else if (event.ctrlKey && event.key.toLowerCase() === 'w') {
        console.log('Ctrl+W pressed - Navigate to next asset');
        event.preventDefault();
        navigationFunctionsRef.current.navigateToNextAsset();
      }
      
      // Ctrl+A: Toggle CML ready state
      else if (event.ctrlKey && event.key === 'a') {
        // Prevent default behavior (select all)
        event.preventDefault();
        
        // Toggle the current active CML's ready state
        if (selectedAsset && selectedAsset.CMLs && selectedAsset.CMLs.length > 0) {
          // Get the active tab ID (which corresponds to the CML index)
          const activeTabElement = document.querySelector('.bp5-tab-panel[aria-hidden="false"]');
          if (activeTabElement) {
            // Find the CML index from the active tab
            const activeTabId = parseInt(activeTabElement.getAttribute('aria-labelledby')?.replace('bp5-tab-title_cmlTabs_', '')) || 0;
            
            // Toggle the ready state of the active CML
            const cml = selectedAsset.CMLs[activeTabId];
            if (cml) {
              // Dispatch action to toggle the CML ready state
              dispatch(updateCmlReady({
                plantName,
                tagName: selectedAsset.Tag_Name,
                cmlIndex: activeTabId,
                ready: !cml.Ready
              }));
            }
          }
        }
      }
    };
    
    // Add the event listener to the document
    document.addEventListener('keydown', handleKeyDown);
    
    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedAsset, dispatch, plantName, navigateToPreviousAsset, navigateToNextAsset]);
  
  // Calculate ready asset percentages by classification
  const readyStats = useMemo(() => {
    // Group assets by template
    const pvAssets = assets.filter(asset => asset.Template === 'PV');
    const stAssets = assets.filter(asset => asset.Template === 'ST');
    
    // Calculate percentages
    const pvReadyCount = pvAssets.filter(asset => asset.Ready).length;
    const stReadyCount = stAssets.filter(asset => asset.Ready).length;
    
    const pvPercentage = pvAssets.length > 0 ? (pvReadyCount / pvAssets.length) * 100 : 0;
    const stPercentage = stAssets.length > 0 ? (stReadyCount / stAssets.length) * 100 : 0;
    const totalPercentage = assets.length > 0 ? 
      (assets.filter(asset => asset.Ready).length / assets.length) * 100 : 0;
    
    return {
      pv: { count: pvAssets.length, ready: pvReadyCount, percentage: pvPercentage },
      st: { count: stAssets.length, ready: stReadyCount, percentage: stPercentage },
      total: { count: assets.length, ready: assets.filter(asset => asset.Ready).length, percentage: totalPercentage }
    };
  }, [assets]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Navbar className="bp5-dark futuristic-navbar" style={{
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.4), 0 0 20px rgba(33, 93, 176, 0.1)',
        background: 'linear-gradient(135deg, #1c2127 0%, #252a31 100%)',
        borderBottom: '1px solid rgba(33, 93, 176, 0.2)'
      }}>
        <Navbar.Group>
          <Navbar.Heading>
            <H4 style={{
              margin: 0,
              color: '#f5f8fa',
              fontWeight: '600',
              letterSpacing: '1px',
              textShadow: '0 0 10px rgba(33, 93, 176, 0.3)'
            }}>
              ASSET MANAGEMENT
            </H4>
          </Navbar.Heading>
        </Navbar.Group>
        <Navbar.Group>
          <div style={{ marginLeft: '20px', display: 'flex', alignItems: 'center', gap: '20px' }}>
            <div style={{ width: '140px', position: 'relative' }}>
              <div style={{
                fontSize: '13px',
                marginBottom: '4px',
                color: '#f5f8fa',
                fontWeight: '500',
                letterSpacing: '0.5px',
                textTransform: 'uppercase'
              }}>
                Pressure Vessels
              </div>
              <div style={{ position: 'relative' }}>
                <ProgressBar
                  value={readyStats.pv.percentage / 100}
                  intent={readyStats.pv.percentage === 100 ? 'success' : 'primary'}
                  stripes={readyStats.pv.percentage < 100}
                  animate={readyStats.pv.percentage < 100}
                  style={{ height: '10px', borderRadius: '5px' }}
                />
                <div style={{
                  position: 'absolute',
                  top: '-3px',
                  right: '0',
                  fontSize: '12px',
                  color: '#215db0',
                  fontWeight: '700',
                  textShadow: '0 0 6px rgba(33, 93, 176, 0.8)',
                  background: 'rgba(28, 33, 39, 0.95)',
                  padding: '2px 6px',
                  borderRadius: '3px',
                  border: '1px solid rgba(33, 93, 176, 0.4)',
                  fontFamily: 'monospace'
                }}>
                  {Math.round(readyStats.pv.percentage)}%
                </div>
              </div>
              <div style={{
                fontSize: '11px',
                marginTop: '4px',
                color: '#8a9ba8',
                fontFamily: 'monospace',
                textAlign: 'center'
              }}>
                {readyStats.pv.ready}/{readyStats.pv.count} ready
              </div>
            </div>

            <div style={{ width: '140px', position: 'relative' }}>
              <div style={{
                fontSize: '13px',
                marginBottom: '4px',
                color: '#f5f8fa',
                fontWeight: '500',
                letterSpacing: '0.5px',
                textTransform: 'uppercase'
              }}>
                Storage Tanks
              </div>
              <div style={{ position: 'relative' }}>
                <ProgressBar
                  value={readyStats.st.percentage / 100}
                  intent={readyStats.st.percentage === 100 ? 'success' : 'warning'}
                  stripes={readyStats.st.percentage < 100}
                  animate={readyStats.st.percentage < 100}
                  style={{ height: '10px', borderRadius: '5px' }}
                />
                <div style={{
                  position: 'absolute',
                  top: '-3px',
                  right: '0',
                  fontSize: '12px',
                  color: '#215db0',
                  fontWeight: '700',
                  textShadow: '0 0 6px rgba(33, 93, 176, 0.8)',
                  background: 'rgba(28, 33, 39, 0.95)',
                  padding: '2px 6px',
                  borderRadius: '3px',
                  border: '1px solid rgba(33, 93, 176, 0.4)',
                  fontFamily: 'monospace'
                }}>
                  {Math.round(readyStats.st.percentage)}%
                </div>
              </div>
              <div style={{
                fontSize: '11px',
                marginTop: '4px',
                color: '#8a9ba8',
                fontFamily: 'monospace',
                textAlign: 'center'
              }}>
                {readyStats.st.ready}/{readyStats.st.count} ready
              </div>
            </div>
          </div>
        </Navbar.Group>
        <Navbar.Group align="right">
          <div style={{ width: '160px', marginRight: '10px', position: 'relative' }}>
            <div style={{
              fontSize: '13px',
              marginBottom: '4px',
              color: '#f5f8fa',
              fontWeight: '500',
              letterSpacing: '0.5px',
              textTransform: 'uppercase'
            }}>
              Total Progress
            </div>
            <div style={{ position: 'relative' }}>
              <ProgressBar
                value={readyStats.total.percentage / 100}
                intent={readyStats.total.percentage === 100 ? 'success' : 'primary'}
                stripes={readyStats.total.percentage < 100}
                animate={readyStats.total.percentage < 100}
                style={{ height: '10px', borderRadius: '5px' }}
              />
              <div style={{
                position: 'absolute',
                top: '-3px',
                right: '0',
                fontSize: '12px',
                color: '#215db0',
                fontWeight: '700',
                textShadow: '0 0 6px rgba(33, 93, 176, 0.8)',
                background: 'rgba(28, 33, 39, 0.95)',
                padding: '2px 6px',
                borderRadius: '3px',
                border: '1px solid rgba(33, 93, 176, 0.4)',
                fontFamily: 'monospace'
              }}>
                {Math.round(readyStats.total.percentage)}%
              </div>
            </div>
            <div style={{
              fontSize: '11px',
              marginTop: '4px',
              color: '#8a9ba8',
              fontFamily: 'monospace',
              textAlign: 'center'
            }}>
              {readyStats.total.ready}/{readyStats.total.count} ready
            </div>
          </div>
          <Divider />
          <Popover
            content={
              <Menu className="bp5-dark">
                {plantsLoading ? (
                  <MenuItem text="Loading plants..." disabled icon={<Spinner size={16} />} />
                ) : plantsError ? (
                  <MenuItem text="Error loading plants" disabled icon="error" />
                ) : plants.length === 0 ? (
                  <MenuItem text="No plants available" disabled icon="warning-sign" />
                ) : (
                  // Sort plants alphabetically by name
                  [...plants]
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map(plant => (
                      <MenuItem 
                        key={plant.name}
                        text={plant.name}
                        icon={plant.name === plantName ? "tick" : "blank"}
                        active={plant.name === plantName}
                        onClick={() => handlePlantChange(plant.name)}
                      />
                    ))
                )}
              </Menu>
            }
            position={Position.BOTTOM_RIGHT}
          >
            <Button
              minimal
              rightIcon="caret-down"
              className="bp5-dark"
            >
              <Tag minimal className="bp5-dark">{plantName}</Tag>
            </Button>
          </Popover>
        </Navbar.Group>
      </Navbar>
      <div style={{ display: 'flex', flex: 1, overflow: 'hidden', backgroundColor: '#1c2127' }}>
        <AssetSidebar />
        <AssetViewer />
      </div>
    </div>
  );
}

export default AssetManagement;
