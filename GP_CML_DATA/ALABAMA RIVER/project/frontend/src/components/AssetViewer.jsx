import React, { useState, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  Card, 
  H3, 
  Checkbox, 
  NonIdealState, 
  Spinner,
  Tag,
  Tabs,
  Tab,
  Intent
} from '@blueprintjs/core';

import 'antd/dist/reset.css';
import '../table-styles.css';

import DraggableCMLTable from './DraggableCMLTable';
import { updateAssetReady, updateCmlReady } from '../redux/assetsSlice';

const AssetViewer = () => {
  const { plantName, selectedAsset, loading } = useSelector(state => state.assets);
  const dispatch = useDispatch();
  
  const [activeTabId, setActiveTabId] = useState(0);
  
  // Define the CML ready toggle function
  const handleCmlReadyToggle = useCallback((cmlIndex, currentValue) => {
    if (selectedAsset && selectedAsset.Tag_Name) {
      console.log(`Toggling CML ${cmlIndex} from ${currentValue} to ${!currentValue}`);
      // Update the CML ready state
      dispatch(updateCmlReady({
        plantName,
        tagName: selectedAsset.Tag_Name,
        cmlIndex,
        ready: !currentValue
      }));
    }
  }, [dispatch, plantName, selectedAsset]);

  // Define the asset ready toggle function
  const handleAssetReadyToggle = useCallback(() => {
    if (selectedAsset && selectedAsset.Tag_Name) {
      console.log(`Toggling asset ${selectedAsset.Tag_Name} from ${selectedAsset.Ready} to ${!selectedAsset.Ready}`);
      dispatch(updateAssetReady({
        plantName,
        tagName: selectedAsset.Tag_Name,
        ready: !selectedAsset.Ready
      }));
    }
  }, [dispatch, plantName, selectedAsset]);
  
  if (loading) {
    return (
      <div className="content-area bp5-dark" style={{ padding: '16px' }}>
        <div style={{ height: '70vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spinner size={50} />
        </div>
      </div>
    );
  }
  
  if (!selectedAsset) {
    return (
      <div className="content-area bp5-dark" style={{ padding: '16px' }}>
        <NonIdealState
          icon="info-sign"
          title="No Asset Selected"
          description="Select an asset from the sidebar to view its details."
        />
      </div>
    );
  }
  
  return (
    <div 
      className="content-area bp5-dark" 
      style={{ padding: '16px', display: 'flex', flexDirection: 'column', height: 'calc(100vh - 50px)', overflow: 'hidden' }}
    >
      <div style={{
        marginBottom: '16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        background: 'linear-gradient(135deg, #252a31 0%, #1c2127 100%)',
        padding: '16px',
        borderRadius: '8px',
        border: '1px solid rgba(0, 212, 255, 0.2)',
        boxShadow: '0 0 20px rgba(0, 212, 255, 0.1)'
      }}>
        <div>
          <H3 style={{
            marginBottom: '8px',
            color: '#f5f8fa',
            fontWeight: '600',
            letterSpacing: '0.5px',
            textShadow: '0 0 10px rgba(0, 212, 255, 0.3)'
          }}>
            {selectedAsset.Tag_Name}
          </H3>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Tag
              intent={Intent.PRIMARY}
              style={{
                background: 'linear-gradient(135deg, #00d4ff, #0099cc)',
                color: 'white',
                fontWeight: '600',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                boxShadow: '0 2px 8px rgba(0, 212, 255, 0.3)'
              }}
            >
              {selectedAsset.Template}
            </Tag>
            <Tag style={{
              background: 'rgba(0, 212, 255, 0.1)',
              color: '#00d4ff',
              border: '1px solid rgba(0, 212, 255, 0.3)',
              fontWeight: '500',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              {selectedAsset.Asset_Classification}
            </Tag>
          </div>
        </div>
        <div>
          <Checkbox
            checked={selectedAsset.Ready}
            label={
              <span style={{
                color: '#f5f8fa',
                fontWeight: '500',
                letterSpacing: '0.5px'
              }}>
                Mark Asset as Ready
              </span>
            }
            onChange={handleAssetReadyToggle}
            disabled={!selectedAsset.CMLs.every(cml => cml.Ready)}
            large
            style={{
              color: '#f5f8fa'
            }}
          />
        </div>
      </div>
      
      <Card className="futuristic-card" style={{ borderRadius: '8px', overflow: 'hidden', padding: 0, flex: 1, display: 'flex', flexDirection: 'column' }}>
        <div style={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          <Tabs 
            id="cmlTabs"
            onChange={setActiveTabId} 
            selectedTabId={activeTabId}
            animate={true}
            renderActiveTabPanelOnly={true}
            className="bp5-dark"
            style={{ display: 'flex', flexDirection: 'column', height: '100%', overflow: 'hidden' }}
            contentStyle={{ flex: 1, position: 'relative', overflow: 'hidden' }}
          >
            {selectedAsset.CMLs.map((cml, index) => (
              <Tab
                key={index}
                id={index}
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      fontWeight: '500',
                      letterSpacing: '0.5px',
                      textTransform: 'uppercase'
                    }}>
                      CML #{index + 1}
                    </span>
                    {cml.Ready && (
                      <Tag
                        minimal
                        intent={Intent.SUCCESS}
                        style={{
                          background: 'rgba(61, 204, 145, 0.2)',
                          color: '#3dcc91',
                          border: '1px solid rgba(61, 204, 145, 0.4)',
                          fontWeight: '600',
                          fontSize: '10px',
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px'
                        }}
                      >
                        Ready
                      </Tag>
                    )}
                  </div>
                }
                panel={<DraggableCMLTable cmlData={cml} cmlIndex={index} onToggleReady={handleCmlReadyToggle} />}
                panelClassName="bp5-dark"
              />
            ))}
          </Tabs>
        </div>
      </Card>
    </div>
  );
};

export default AssetViewer;
