import React, { useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Card, 
  Icon, 
  Tab, 
  Tabs, 
  Spinner, 
  NonIdealState,
  Classes,
  Intent,
  H4,
  H5,
  Text,
  Tag,
  Divider,
  Menu,
  MenuItem,
  ButtonGroup,
  Button
} from '@blueprintjs/core';
import { selectAsset } from '../redux/assetsSlice';

export default function AssetSidebar() {
  const dispatch = useDispatch();
  const { assets, selectedAsset, loading, error } = useSelector(state => state.assets);
  const [readinessFilter, setReadinessFilter] = useState('all'); // 'all', 'ready', 'not-ready'
  const [assetClassFilter, setAssetClassFilter] = useState('all'); // 'all', 'PRESSURE VESSEL', 'HEAT EXCHANGER', etc.
  
  // Filter assets by readiness and asset classification
  const filterAssets = (assetsList) => {
    let filteredAssets = assetsList;
    
    // Apply readiness filter
    if (readinessFilter === 'ready') {
      filteredAssets = filteredAssets.filter(asset => asset.Ready);
    } else if (readinessFilter === 'not-ready') {
      filteredAssets = filteredAssets.filter(asset => !asset.Ready);
    }
    
    // Apply asset classification filter
    if (assetClassFilter !== 'all') {
      filteredAssets = filteredAssets.filter(asset => asset.Asset_Classification === assetClassFilter);
    }
    
    return filteredAssets;
  };
  
  // Group assets by template and apply filters
  const pvAssets = filterAssets(assets.filter(asset => asset.Template === 'PV'));
  const stAssets = filterAssets(assets.filter(asset => asset.Template === 'ST'));
  const pressureVesselAssets = filterAssets(assets.filter(asset => asset.Template === 'PRESSURE VESSEL'));
  const heatExchangerAssets = filterAssets(assets.filter(asset => asset.Template === 'HEAT EXCHANGER'));
  
  // Get unique asset classifications for filter dropdown
  const assetClassifications = useMemo(() => {
    const classifications = new Set();
    assets.forEach(asset => {
      if (asset.Asset_Classification) {
        classifications.add(asset.Asset_Classification);
      }
    });
    return ['all', ...Array.from(classifications)];
  }, [assets]);
  
  const handleAssetClick = (asset) => {
    dispatch(selectAsset(asset));
  };
  
  const renderAssetList = (assetList) => {
    if (assetList.length === 0) {
      return (
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <NonIdealState
            icon="folder-open"
            title="No assets found"
            description="There are no assets in this category."
            iconSize={40}
          />
        </div>
      );
    }
    
    return (
      <div style={{ padding: '8px' }}>
        {assetList.map(asset => (
          <Card 
            key={asset.Tag_Name} 
            interactive={true} 
            onClick={() => handleAssetClick(asset)}
            style={{ 
              marginBottom: '8px', 
              padding: '10px',
              backgroundColor: selectedAsset?.Tag_Name === asset.Tag_Name ? 'rgba(19, 124, 189, 0.2)' : undefined,
              boxShadow: '0 0 0 1px rgba(16, 22, 26, 0.4), 0 1px 2px rgba(16, 22, 26, 0.2)'
            }}
            className={`asset-card ${selectedAsset?.Tag_Name === asset.Tag_Name ? 'selected' : ''} ${asset.Ready ? 'ready' : 'not-ready'}`}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text style={{ fontWeight: 'bold', fontSize: '14px' }}>{asset.Tag_Name}</Text>
                <Text style={{ fontSize: '12px', opacity: 0.7, marginTop: '4px' }}>{asset.Asset_Classification}</Text>
              </div>
              <Icon 
                icon={asset.Ready ? "tick-circle" : "circle"} 
                intent={asset.Ready ? Intent.SUCCESS : Intent.WARNING}
                size={16}
              />
            </div>
          </Card>
        ))}
      </div>
    );
  };
  
  if (loading) {
    return (
      <div className="sidebar bp5-dark" style={{ width: '280px', padding: '12px', borderRight: '1px solid rgba(255, 255, 255, 0.1)', height: '100vh', overflowY: 'auto' }}>
        <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spinner size={50} />
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="sidebar bp5-dark" style={{ width: '280px', padding: '12px', borderRight: '1px solid rgba(255, 255, 255, 0.1)', height: '100vh', overflowY: 'auto' }}>
        <NonIdealState
          icon="error"
          title="Error loading assets"
          description={error}
        />
      </div>
    );
  }
  
  return (
    <div className="sidebar bp5-dark" style={{ width: '280px', padding: '12px', borderRight: '1px solid rgba(255, 255, 255, 0.1)', height: '100vh', overflowY: 'auto' }}>
      <div style={{ marginBottom: '16px', paddingBottom: '8px', borderBottom: '1px solid rgba(255, 255, 255, 0.2)' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
          <H4 style={{ margin: 0 }}>Assets</H4>
        </div>
        <div style={{ marginTop: '10px' }}>
          <Text style={{ marginBottom: '5px', display: 'block' }}>Filter by readiness:</Text>
          <ButtonGroup fill={true}>
            <Button 
              text="All" 
              small={true}
              active={readinessFilter === 'all'}
              onClick={() => setReadinessFilter('all')}
              className="filter-button"
            />
            <Button 
              text="Ready" 
              small={true}
              icon="tick-circle"
              intent={readinessFilter === 'ready' ? Intent.SUCCESS : undefined}
              active={readinessFilter === 'ready'}
              onClick={() => setReadinessFilter('ready')}
              className="filter-button"
            />
            <Button 
              text="Not Ready" 
              small={true}
              icon="circle"
              intent={readinessFilter === 'not-ready' ? Intent.WARNING : undefined}
              active={readinessFilter === 'not-ready'}
              onClick={() => setReadinessFilter('not-ready')}
              className="filter-button"
            />
          </ButtonGroup>
        </div>
        
        <div style={{ marginTop: '10px' }}>
          <Text style={{ marginBottom: '5px', display: 'block' }}>Filter by asset class:</Text>
          <div className="bp5-select" style={{ width: '100%' }}>
            <select 
              value={assetClassFilter} 
              onChange={(e) => setAssetClassFilter(e.target.value)}
              style={{ width: '100%' }}
            >
              {assetClassifications.map(classification => (
                <option key={classification} value={classification}>
                  {classification === 'all' ? 'All Asset Classes' : classification}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
      <Card className="futuristic-card" style={{ borderRadius: '8px', overflow: 'hidden', padding: 0 }}>
        <Tabs id="assetTabs" animate={true} renderActiveTabPanelOnly={true} className="bp5-dark">
          <Tab 
            id="pv" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="dashboard" size={14} />
                <span>PV ({pvAssets.length})</span>
              </div>
            }
            panel={renderAssetList(pvAssets)}
          />
          <Tab 
            id="st" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="flame" size={14} />
                <span>ST ({stAssets.length})</span>
              </div>
            }
            panel={renderAssetList(stAssets)}
          />
          <Tab 
            id="pressure-vessel" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="compressed" size={14} />
                <span>PRESSURE VESSEL ({pressureVesselAssets.length})</span>
              </div>
            }
            panel={renderAssetList(pressureVesselAssets)}
          />
          <Tab 
            id="heat-exchanger" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="heat-grid" size={14} />
                <span>HEAT EXCHANGER ({heatExchangerAssets.length})</span>
              </div>
            }
            panel={renderAssetList(heatExchangerAssets)}
          />
        </Tabs>
      </Card>
    </div>
  );
}