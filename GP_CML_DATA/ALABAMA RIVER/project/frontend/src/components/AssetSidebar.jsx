import React, { useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Card, 
  Icon, 
  Tab, 
  Tabs, 
  Spinner, 
  NonIdealState,
  Classes,
  Intent,
  H4,
  H5,
  Text,
  Tag,
  Divider,
  Menu,
  MenuItem,
  ButtonGroup,
  Button
} from '@blueprintjs/core';
import { selectAsset } from '../redux/assetsSlice';

export default function AssetSidebar() {
  const dispatch = useDispatch();
  const { assets, selectedAsset, loading, error } = useSelector(state => state.assets);
  const [readinessFilter, setReadinessFilter] = useState('all'); // 'all', 'ready', 'not-ready'
  const [assetClassFilter, setAssetClassFilter] = useState('all'); // 'all', 'PRESSURE VESSEL', 'HEAT EXCHANGER', etc.
  
  // Filter assets by readiness and asset classification
  const filterAssets = (assetsList) => {
    let filteredAssets = assetsList;
    
    // Apply readiness filter
    if (readinessFilter === 'ready') {
      filteredAssets = filteredAssets.filter(asset => asset.Ready);
    } else if (readinessFilter === 'not-ready') {
      filteredAssets = filteredAssets.filter(asset => !asset.Ready);
    }
    
    // Apply asset classification filter
    if (assetClassFilter !== 'all') {
      filteredAssets = filteredAssets.filter(asset => asset.Asset_Classification === assetClassFilter);
    }
    
    return filteredAssets;
  };
  
  // Group assets by template and apply filters
  const pvAssets = filterAssets(assets.filter(asset => asset.Template === 'PV'));
  const stAssets = filterAssets(assets.filter(asset => asset.Template === 'ST'));
  const pressureVesselAssets = filterAssets(assets.filter(asset => asset.Template === 'PRESSURE VESSEL'));
  const heatExchangerAssets = filterAssets(assets.filter(asset => asset.Template === 'HEAT EXCHANGER'));
  
  // Get unique asset classifications for filter dropdown
  const assetClassifications = useMemo(() => {
    const classifications = new Set();
    assets.forEach(asset => {
      if (asset.Asset_Classification) {
        classifications.add(asset.Asset_Classification);
      }
    });
    return ['all', ...Array.from(classifications)];
  }, [assets]);
  
  const handleAssetClick = (asset) => {
    dispatch(selectAsset(asset));
  };
  
  const renderAssetList = (assetList) => {
    if (assetList.length === 0) {
      return (
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <NonIdealState
            icon="folder-open"
            title="No assets found"
            description="There are no assets in this category."
            iconSize={40}
          />
        </div>
      );
    }
    
    return (
      <div style={{
        padding: '8px',
        height: '100%',
        overflowY: 'auto',
        overflowX: 'hidden'
      }}>
        {assetList.map(asset => (
          <Card
            key={asset.Tag_Name}
            interactive={true}
            onClick={() => handleAssetClick(asset)}
            style={{
              marginBottom: '8px',
              padding: '10px',
              backgroundColor: selectedAsset?.Tag_Name === asset.Tag_Name ? 'rgba(33, 93, 176, 0.2)' : undefined,
            }}
            className={`asset-card futuristic-card ${selectedAsset?.Tag_Name === asset.Tag_Name ? 'selected' : ''} ${asset.Ready ? 'ready' : 'not-ready'}`}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text style={{
                  fontWeight: '600',
                  fontSize: '14px',
                  color: '#f5f8fa',
                  letterSpacing: '0.5px'
                }}>
                  {asset.Tag_Name}
                </Text>
                <Text style={{
                  fontSize: '12px',
                  color: '#8f99a8',
                  marginTop: '4px',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  {asset.Asset_Classification}
                </Text>
              </div>
              <Icon
                icon={asset.Ready ? "tick-circle" : "circle"}
                intent={asset.Ready ? Intent.SUCCESS : Intent.WARNING}
                size={16}
                style={{
                  filter: asset.Ready ? 'drop-shadow(0 0 4px rgba(28, 110, 66, 0.5))' : 'drop-shadow(0 0 4px rgba(147, 86, 16, 0.5))'
                }}
              />
            </div>
          </Card>
        ))}
      </div>
    );
  };
  
  if (loading) {
    return (
      <div className="sidebar bp5-dark" style={{ width: '280px', padding: '12px', borderRight: '1px solid rgba(255, 255, 255, 0.1)', height: '100vh', overflowY: 'auto' }}>
        <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spinner size={50} />
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="sidebar bp5-dark" style={{ width: '280px', padding: '12px', borderRight: '1px solid rgba(255, 255, 255, 0.1)', height: '100vh', overflowY: 'auto' }}>
        <NonIdealState
          icon="error"
          title="Error loading assets"
          description={error}
        />
      </div>
    );
  }
  
  return (
    <div className="sidebar bp5-dark" style={{
      width: '280px',
      borderRight: '1px solid rgba(255, 255, 255, 0.1)',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* Fixed Filter Controls */}
      <div style={{
        padding: '12px',
        paddingBottom: '8px',
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
        background: 'linear-gradient(135deg, #252a31 0%, #1c2127 100%)',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
        position: 'relative',
        zIndex: 10
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
          <H4 style={{
            margin: 0,
            color: '#f5f8fa',
            fontWeight: '600',
            letterSpacing: '0.5px',
            textTransform: 'uppercase'
          }}>
            Assets
          </H4>
        </div>
        <div style={{ marginTop: '10px' }}>
          <Text style={{
            marginBottom: '5px',
            display: 'block',
            color: '#8f99a8',
            fontSize: '12px',
            fontWeight: '500',
            textTransform: 'uppercase',
            letterSpacing: '0.5px'
          }}>
            Filter by readiness:
          </Text>
          <ButtonGroup fill={true}>
            <Button
              text="All"
              small={true}
              active={readinessFilter === 'all'}
              onClick={() => setReadinessFilter('all')}
              className="filter-button"
            />
            <Button
              text="Ready"
              small={true}
              icon="tick-circle"
              intent={readinessFilter === 'ready' ? Intent.SUCCESS : undefined}
              active={readinessFilter === 'ready'}
              onClick={() => setReadinessFilter('ready')}
              className="filter-button"
            />
            <Button
              text="Not Ready"
              small={true}
              icon="circle"
              intent={readinessFilter === 'not-ready' ? Intent.WARNING : undefined}
              active={readinessFilter === 'not-ready'}
              onClick={() => setReadinessFilter('not-ready')}
              className="filter-button"
            />
          </ButtonGroup>
        </div>

        <div style={{ marginTop: '10px' }}>
          <Text style={{
            marginBottom: '5px',
            display: 'block',
            color: '#8f99a8',
            fontSize: '12px',
            fontWeight: '500',
            textTransform: 'uppercase',
            letterSpacing: '0.5px'
          }}>
            Filter by asset class:
          </Text>
          <div className="bp5-select" style={{ width: '100%' }}>
            <select
              value={assetClassFilter}
              onChange={(e) => setAssetClassFilter(e.target.value)}
              style={{ width: '100%' }}
            >
              {assetClassifications.map(classification => (
                <option key={classification} value={classification}>
                  {classification === 'all' ? 'All Asset Classes' : classification}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Scrollable Asset Cards */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Card className="futuristic-card" style={{
          borderRadius: '0',
          overflow: 'hidden',
          padding: 0,
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
        <Tabs id="assetTabs" animate={true} renderActiveTabPanelOnly={true} className="bp5-dark">
          <Tab 
            id="pv" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="dashboard" size={14} />
                <span>PV ({pvAssets.length})</span>
              </div>
            }
            panel={renderAssetList(pvAssets)}
          />
          <Tab 
            id="st" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="flame" size={14} />
                <span>ST ({stAssets.length})</span>
              </div>
            }
            panel={renderAssetList(stAssets)}
          />
          <Tab 
            id="pressure-vessel" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="compressed" size={14} />
                <span>PRESSURE VESSEL ({pressureVesselAssets.length})</span>
              </div>
            }
            panel={renderAssetList(pressureVesselAssets)}
          />
          <Tab 
            id="heat-exchanger" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Icon icon="heat-grid" size={14} />
                <span>HEAT EXCHANGER ({heatExchangerAssets.length})</span>
              </div>
            }
            panel={renderAssetList(heatExchangerAssets)}
          />
        </Tabs>
        </Card>
      </div>
    </div>
  );
}