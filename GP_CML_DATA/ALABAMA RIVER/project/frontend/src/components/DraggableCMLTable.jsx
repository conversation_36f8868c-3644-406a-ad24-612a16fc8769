import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, message, Tag, ConfigProvider, theme, Checkbox as AntCheckbox } from 'antd';
import { HolderOutlined, DownloadOutlined } from '@ant-design/icons';
import './checkbox-styles.css';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { H5, Checkbox } from '@blueprintjs/core';

// Row component for the sortable table
// Simple row component that supports drag and drop
const Row = ({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key']
  });

  // Get the data-is-selected attribute from props
  const isSelected = props['data-is-selected'] === 'true';
  const isPartOfDragGroup = props['data-is-part-of-drag-group'] === 'true';

  const style = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 }),
    transition,
    fontSize: '13px',
    ...(isDragging ? { 
      position: 'relative', 
      zIndex: 9999, 
      background: 'rgba(33, 93, 176, 0.15)',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
    } : {}),
    ...(isPartOfDragGroup && !isDragging ? { 
      opacity: 0.6,
      background: 'rgba(33, 93, 176, 0.1)' 
    } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      {children}
    </tr>
  );
};

// Custom cell component to style the drag handle
const Cell = ({ children, ...props }) => {
  // Check if this is the drag handle cell (first column)
  const isDragHandle = props.className && props.className.includes('drag-visible');
  
  if (isDragHandle) {
    return (
      <td 
        {...props} 
        style={{ 
          ...props.style, 
          cursor: 'grab',
          backgroundColor: '#1C2127'
        }}
      >
        {children}
      </td>
    );
  }
  
  // Regular cell
  return <td {...props}>{children}</td>;
};

const DraggableCMLTable = ({ cmlData, cmlIndex, onToggleReady }) => {
  const [rowData, setRowData] = useState([]);
  const [rowOrder, setRowOrder] = useState([]);
  const [orderModalVisible, setOrderModalVisible] = useState(false);
  const [orderJson, setOrderJson] = useState('');
  const [selectedRows, setSelectedRows] = useState([]);
  const [firstSelectedRow, setFirstSelectedRow] = useState(null);
  
  // Sensors for drag and drop
  const sensors = useSensors(useSensor(PointerSensor, {
    activationConstraint: { distance: 1 }
  }));

  // Process CML data to create row data
  useEffect(() => {
    if (cmlData && cmlData.DataFrame) {
      const { data, columns } = cmlData.DataFrame;
      
      // Create row data with proper format
      const processedData = data.map((row, rowIdx) => {
        const rowObj = { 
          key: rowIdx.toString(),
          id: rowIdx.toString() 
        };
        
        // Add all column data
        columns.forEach((col, colIdx) => {
          rowObj[col] = row[colIdx];
        });
        
        return rowObj;
      });
      
      setRowData(processedData);
      setRowOrder(processedData.map(row => row.id));
    }
  }, [cmlData]);

  // Handle row selection
  const handleRowSelection = (rowId) => {
    // If this is the first selection
    if (selectedRows.length === 0) {
      setSelectedRows([rowId]);
      setFirstSelectedRow(rowId);
      return;
    }
    
    // If this row is already selected, deselect it if it's at the edge of the selection
    if (selectedRows.includes(rowId)) {
      // Only allow deselection if it's at the edge of the selection range
      const rowIndex = parseInt(rowId);
      const selectedIndices = selectedRows.map(id => parseInt(id)).sort((a, b) => a - b);
      
      // Check if it's at the beginning or end of the selection
      if (rowIndex === selectedIndices[0] || rowIndex === selectedIndices[selectedIndices.length - 1]) {
        setSelectedRows(selectedRows.filter(id => id !== rowId));
        // If we're removing the first selected row and there are still selections, update firstSelectedRow
        if (rowId === firstSelectedRow && selectedRows.length > 1) {
          setFirstSelectedRow(selectedRows.find(id => id !== rowId));
        }
      }
      return;
    }
    
    // Only allow selection of adjacent rows
    const currentRowIndex = parseInt(rowId);
    const firstRowIndex = parseInt(firstSelectedRow);
    
    // Get all currently selected row indices
    const selectedIndices = selectedRows.map(id => parseInt(id));
    const minSelected = Math.min(...selectedIndices);
    const maxSelected = Math.max(...selectedIndices);
    
    // Only allow selection if it's adjacent to the current selection
    if (currentRowIndex === minSelected - 1 || currentRowIndex === maxSelected + 1) {
      setSelectedRows([...selectedRows, rowId]);
    }
  };

  // Track which rows are part of the current drag group
  const [dragGroupRows, setDragGroupRows] = useState([]);

  // Handle drag start
  const handleDragStart = (event) => {
    const { active } = event;
    const isSelected = selectedRows.includes(active.id);
    
    // If dragging a selected row and there are multiple selections,
    // mark all selected rows as part of the drag group
    if (isSelected && selectedRows.length > 1) {
      setDragGroupRows(selectedRows);
    } else {
      setDragGroupRows([]);
    }
  };

  // Handle row drag end
  const handleRowDragEnd = (event) => {
    const { active, over } = event;
    
    if (active.id !== over.id) {
      const oldIndex = rowOrder.indexOf(active.id);
      const newIndex = rowOrder.indexOf(over.id);
      
      // Check if the dragged row is selected
      const isSelected = selectedRows.includes(active.id);
      
      if (isSelected && selectedRows.length > 1) {
        // Move all selected rows as a group
        const newRowOrder = [...rowOrder];
        
        // Sort selected rows by their current index
        const selectedIndices = selectedRows
          .map(id => ({ id, index: rowOrder.indexOf(id) }))
          .sort((a, b) => a.index - b.index);
        
        // Calculate if we're moving up or down
        const firstSelectedIndex = Math.min(...selectedIndices.map(item => item.index));
        const lastSelectedIndex = Math.max(...selectedIndices.map(item => item.index));
        const isMovingUp = newIndex < firstSelectedIndex;
        
        // Remove all selected rows from their current positions
        const removedRows = [];
        selectedIndices.forEach(({ id }) => {
          const currentIndex = newRowOrder.indexOf(id);
          if (currentIndex !== -1) {
            removedRows.push(newRowOrder.splice(currentIndex, 1)[0]);
          }
        });
        
        // Calculate the insertion index
        let insertIndex;
        if (isMovingUp) {
          insertIndex = newRowOrder.indexOf(over.id);
        } else {
          // When moving down, adjust the index to account for the removed rows
          insertIndex = newRowOrder.indexOf(over.id) + 1;
        }
        
        if (insertIndex === -1) insertIndex = 0;
        
        // Insert all removed rows at the new position
        newRowOrder.splice(insertIndex, 0, ...removedRows);
        
        setRowOrder(newRowOrder);
      } else {
        // Move just the single dragged row
        const newRowOrder = [...rowOrder];
        const [removed] = newRowOrder.splice(oldIndex, 1);
        newRowOrder.splice(newIndex, 0, removed);
        
        setRowOrder(newRowOrder);
      }
    }
    
    // Clear drag group when drag ends
    setDragGroupRows([]);
  };
  
  // Generate order JSON
  const generateOrderJson = () => {
    // Get the current order of rows
    const orderedRows = rowOrder.map(id => {
      return rowData.find(row => row.id === id);
    }).filter(Boolean);
    
    // Get the Tag Name from the first row
    const tagName = orderedRows.length > 0 ? (orderedRows[0]['Tag Name'] || '') : '';
    
    // Create simple lists of CML Description and Component Location
    const descriptions = orderedRows.map(row => row['CML Description'] || '');
    const locations = orderedRows.map(row => row['Component Location'] || '');
    
    // Create a simplified JSON structure with Tag Name and order lists
    const orderData = {
      "Tag Name": tagName,
      "order": {
        descriptions: descriptions,
        locations: locations
      }
    };
    
    const jsonString = JSON.stringify(orderData, null, 2);
    setOrderJson(jsonString);
    setOrderModalVisible(true);
  };
  
  // Copy order JSON to clipboard
  const copyOrderJson = () => {
    navigator.clipboard.writeText(orderJson)
      .then(() => {
        message.success('Copied to clipboard');
      })
      .catch(() => {
        message.error('Failed to copy');
      });
  };
  


  // Get ordered row data
  const orderedRowData = rowOrder
    .map(id => rowData.find(row => row.id === id))
    .filter(Boolean);

  // Define columns with compact styling
  const columns = [
    {
      title: '',
      dataIndex: 'select',
      width: 60,
      fixed: 'left',
      align: 'center',
      render: (_, record) => (
        <div style={{ padding: '4px' }}>
          <AntCheckbox 
            checked={selectedRows.includes(record.id)} 
            onChange={() => handleRowSelection(record.id)}
            style={{ 
              margin: 0,
              cursor: 'pointer'
            }}
            className="custom-checkbox"
          />
        </div>
      )
    },
    {
      title: '',
      dataIndex: 'sort',
      width: 30,
      className: 'drag-visible',
      render: () => <HolderOutlined style={{ cursor: 'grab', color: '#a7b6c2', fontSize: '14px' }} />,
      fixed: 'left',
      align: 'center'
    }
  ];
  
  // Add columns from the data
  if (rowData.length > 0 && cmlData && cmlData.DataFrame) {
    const { columns: dataColumns } = cmlData.DataFrame;
    
    // Add all columns from the DataFrame
    dataColumns.forEach(col => {
      if (!['key', 'id', 'sort'].includes(col)) {
        columns.push({
          title: col,
          dataIndex: col,
          key: col,
          // Auto-size columns based on content
          ellipsis: false,
          // Special handling for specific columns
          ...(col === 'Component Location' || col === 'CML Description' ? {
            // Allow these columns to resize to fit content
            onCell: () => ({
              style: { whiteSpace: 'normal', wordBreak: 'break-word' }
            })
          } : {}),
          className: 'compact-cell'
        });
      }
    });
  }

  if (!cmlData || !cmlData.DataFrame) {
    return (
      <div style={{ padding: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <H5 style={{ margin: 0 }}>CML Data</H5>
          <Checkbox
            checked={cmlData?.Ready}
            label="Mark CML as Ready"
            onChange={() => onToggleReady(cmlIndex, cmlData?.Ready)}
          />
        </div>
        <div style={{ padding: '20px', textAlign: 'center' }}>
          No data available for this CML
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '16px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <H5 style={{ margin: 0, marginRight: '16px' }}>CML Data</H5>
          <div>
            <Button 
              type="primary" 
              onClick={generateOrderJson}
              style={{
                backgroundColor: '#238551',
                borderColor: '#238551',
                fontWeight: 'bold',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                borderRadius: '3px'
              }}
              size="small"
            >
              Generate Order
            </Button>
          </div>
        </div>
        <Checkbox
          checked={cmlData.Ready}
          label="Mark CML as Ready"
          onChange={() => onToggleReady(cmlIndex, cmlData.Ready)}
        />
      </div>
      
      <ConfigProvider
        theme={{
          algorithm: theme.darkAlgorithm,
          token: {
            colorBgContainer: '#1C2127',
            colorBgElevated: '#1C2127',
            colorBorderSecondary: '#394b59',
            colorText: '#f5f8fa',
            colorTextSecondary: '#a7b6c2',
            colorPrimary: '#137cbd',
            colorSuccess: '#0f9960',
            colorWarning: '#d9822b',
            colorError: '#db3737',
            borderRadius: 0,
            fontSize: 13,
            fontFamily: 'Segoe UI, Roboto, Helvetica, Arial, sans-serif',
            lineHeight: 1.5,
            colorBgContainerDisabled: '#394b59',
            colorBgTextHover: '#394b59',
            colorBgTextActive: '#394b59',
          },
          components: {
            Table: {
              colorBgContainer: '#1C2127',
              colorText: '#f5f8fa',
              fontSize: 13,
              headerBg: '#404854',
              headerColor: '#f5f8fa',
              headerSplitColor: '#404854',
              rowHoverBg: '#394b59',
              borderColor: '#394b59',
              headerBorderRadius: 0,
            }
          }
        }}
      >
        <DndContext 
          sensors={sensors} 
          onDragStart={handleDragStart}
          onDragEnd={handleRowDragEnd}
        >
          <SortableContext
            items={orderedRowData.map(i => i.id)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              components={{
                body: {
                  row: Row,
                  cell: Cell
                },
              }}
              rowKey="id"
              columns={columns}
              dataSource={orderedRowData}
              size="small"
              scroll={{ x: true }}
              pagination={false}
              bordered
              className="cml-table bp5-dark compact-table futuristic-table"
              style={{
                backgroundColor: '#1C2127',
                color: '#f5f8fa',
                fontSize: '13px',
                tableLayout: 'auto',
                borderRadius: '8px',
                overflow: 'hidden',
                boxShadow: '0 0 20px rgba(0, 212, 255, 0.1), 0 4px 12px rgba(0, 0, 0, 0.3)'
              }}
              onRow={(record) => {
                // Apply special styling for ROOF, BOTTOM, and HEAD components
                const componentType = record['Component Type'];
                const componentLocation = record['Component Location'];
                let rowStyle = {};
                
                if (componentType === 'HEAD' || 
                    componentLocation?.includes('HEAD') || 
                    componentLocation?.includes('/HEAD')) {
                  rowStyle.backgroundColor = 'rgba(138, 187, 255, 0.5)'; // Transparent version of #8ABBFF for heads
                } else if (componentType === 'ROOF' || 
                           componentLocation?.includes('ROOF') || 
                           componentLocation?.includes('/ROOF')) {
                  rowStyle.backgroundColor = 'rgba(114, 202, 155, 0.5)'; // Transparent version of #72CA9B for roof
                } else if (componentType === 'BOTTOM' || 
                           componentLocation?.includes('BOTTOM') || 
                           componentLocation?.includes('/BOTTOM')) {
                  rowStyle.backgroundColor = 'rgba(251, 179, 96, 0.5)'; // Transparent version of #FBB360 for bottom
                }
                
                // Add selected row styling
                if (selectedRows.includes(record.id)) {
                  rowStyle.backgroundColor = 'rgba(33, 93, 176, 0.3)';
                  rowStyle.border = '1px solid #215DB0';
                }
                
                return {
                  'data-is-selected': selectedRows.includes(record.id).toString(),
                  'data-is-part-of-drag-group': dragGroupRows.includes(record.id).toString(),
                  style: rowStyle,
                  onClick: (event) => {
                    // Allow row click to also toggle selection, but not when clicking on other interactive elements
                    if (!event.target.closest('.ant-checkbox') && 
                        !event.target.closest('.drag-visible')) {
                      handleRowSelection(record.id);
                    }
                  }
                };
              }}
            />
          </SortableContext>
        </DndContext>
      </ConfigProvider>
      
      {/* Order JSON Modal */}
      <Modal
        title="CML Order JSON"
        open={orderModalVisible}
        onCancel={() => setOrderModalVisible(false)}
        footer={[
          <Button key="copy" onClick={copyOrderJson}>
            Copy to Clipboard
          </Button>,
          <Button key="close" type="primary" onClick={() => setOrderModalVisible(false)}>
            Close
          </Button>,
        ]}
        width={700}
      >
        <pre style={{ 
          backgroundColor: '#1C2127', 
          color: '#f5f8fa', 
          padding: '16px',
          borderRadius: '0px',
          maxHeight: '400px',
          overflow: 'auto',
          fontFamily: 'Consolas, Monaco, monospace',
          fontSize: '11px',
          lineHeight: '1.5',
          border: '1px solid #394b59'
        }}>
          {orderJson}
        </pre>
      </Modal>
    </div>
  );
};

export default DraggableCMLTable;
