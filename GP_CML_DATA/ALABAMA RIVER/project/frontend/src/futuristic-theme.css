/* Elegant Theme - Clean and Professional */

/* Global Variables - Using Official Blueprint.js v5 Colors */
:root {
  --bp-primary-blue: #215db0;
  --bp-secondary-blue: #2d72d2;
  --bp-accent-blue: #48aff0;
  --bp-dark-text: #1c2127;
  --bp-darker-bg: #1c2127;
  --bp-card-bg: #252a31;
  --bp-text-primary: #f5f8fa;
  --bp-text-secondary: #5f6b7c;
  --bp-text-muted: #8f99a8;
  --bp-text-disabled: #abb3bf;
  --bp-success-green: #1c6e42;
  --bp-warning-orange: #935610;
  --bp-error-red: #ac2f33;
  --bp-light-bg: #f6f7f9;
  --bp-border-color: rgba(33, 93, 176, 0.15);
  --bp-subtle-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  --bp-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Clean Navbar */
.futuristic-navbar {
  position: relative;
  border-bottom: 1px solid rgba(33, 93, 176, 0.2);
}

/* Clean Progress Bars */
.bp5-progress-bar .bp5-progress-meter {
  background: linear-gradient(90deg, var(--bp-primary-blue), var(--bp-secondary-blue));
  border-radius: 4px;
  transition: all 0.3s ease;
}

.bp5-progress-bar.bp5-intent-success .bp5-progress-meter {
  background: linear-gradient(90deg, var(--bp-success-green), #2d8659);
}

.bp5-progress-bar.bp5-intent-warning .bp5-progress-meter {
  background: linear-gradient(90deg, var(--bp-warning-orange), #b8691a);
}

/* Clean Cards */
.futuristic-card {
  background: var(--bp-card-bg);
  border: 1px solid var(--bp-border-color);
  border-radius: 8px;
  box-shadow: var(--bp-card-shadow);
  transition: all 0.3s ease;
  position: relative;
}

.futuristic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--bp-primary-blue), var(--bp-accent-blue));
  opacity: 0.6;
}

.futuristic-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  border-color: var(--bp-primary-blue);
}

/* Clean Table Styling */
.futuristic-table {
  background: var(--bp-darker-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--bp-card-shadow);
}

.futuristic-table .ant-table-thead > tr > th {
  background: var(--bp-card-bg);
  border-bottom: 1px solid var(--bp-border-color);
  color: var(--bp-text-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  padding: 12px 8px;
}

.futuristic-table .ant-table-tbody > tr > td {
  background: var(--bp-darker-bg);
  border-bottom: 1px solid var(--bp-border-color);
  color: var(--bp-text-primary);
  font-size: 13px;
  padding: 8px;
  transition: all 0.2s ease;
}

.futuristic-table .ant-table-tbody > tr:hover > td {
  background: rgba(33, 93, 176, 0.05);
}

/* Component Type Row Colors - Clean */
.head-row {
  background: rgba(138, 187, 255, 0.08) !important;
  border-left: 2px solid #8abbff;
}

.head-row:hover > td {
  background: rgba(138, 187, 255, 0.12) !important;
}

.roof-row {
  background: rgba(114, 202, 155, 0.08) !important;
  border-left: 2px solid #72ca9b;
}

.roof-row:hover > td {
  background: rgba(114, 202, 155, 0.12) !important;
}

.bottom-row {
  background: rgba(251, 179, 96, 0.08) !important;
  border-left: 2px solid #fbb360;
}

.bottom-row:hover > td {
  background: rgba(251, 179, 96, 0.12) !important;
}

/* Clean Asset Cards */
.asset-card {
  background: var(--bp-card-bg);
  border: 1px solid var(--bp-border-color);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.asset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--bp-text-muted);
  transition: all 0.3s ease;
}

.asset-card:hover {
  transform: translateX(2px);
  box-shadow: var(--bp-card-shadow);
  border-color: var(--bp-primary-blue);
}

.asset-card:hover::before {
  background: var(--bp-primary-blue);
}

.asset-card.selected {
  border-color: var(--bp-primary-blue);
  background: rgba(33, 93, 176, 0.05);
}

.asset-card.selected::before {
  background: var(--bp-primary-blue);
}

.asset-card.ready::before {
  background: var(--bp-success-green);
}

.asset-card.not-ready::before {
  background: var(--bp-warning-orange);
}

/* Clean Tabs */
.bp5-tab-list {
  background: var(--bp-darker-bg);
  border-bottom: 1px solid var(--bp-border-color);
}

.bp5-tab {
  color: var(--bp-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
}

.bp5-tab[aria-selected="true"] {
  color: var(--bp-primary-blue);
  box-shadow: inset 0 -2px 0 var(--bp-primary-blue);
}

.bp5-tab:hover {
  color: var(--bp-text-primary);
}

/* Clean Buttons */
.bp5-button.futuristic-btn {
  background: linear-gradient(135deg, var(--bp-primary-blue), var(--bp-secondary-blue));
  border: none;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.bp5-button.futuristic-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--bp-card-shadow);
}

/* Clean Checkboxes */
.custom-checkbox .ant-checkbox-inner {
  background: var(--bp-darker-bg);
  border: 2px solid var(--bp-border-color);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.custom-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background: linear-gradient(135deg, var(--bp-primary-blue), var(--bp-secondary-blue));
  border-color: var(--bp-primary-blue);
}

/* Clean Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bp-darker-bg);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--bp-primary-blue), var(--bp-secondary-blue));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--bp-accent-blue);
}

/* Clean Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Clean Navbar Enhancement */
.bp5-navbar.futuristic-navbar {
  position: relative;
  background: var(--bp-darker-bg);
  border-bottom: 1px solid var(--bp-border-color);
}

/* Clean Button Hover Effects */
.bp5-button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Clean Input Styling */
.bp5-input {
  background: rgba(28, 33, 39, 0.8);
  border: 1px solid var(--bp-border-color);
  color: var(--bp-text-primary);
  transition: all 0.3s ease;
}

.bp5-input:focus {
  border-color: var(--bp-primary-blue);
  box-shadow: 0 0 0 2px rgba(33, 93, 176, 0.2);
}

/* Clean Dropdown Styling */
.bp5-menu {
  background: var(--bp-card-bg);
  border: 1px solid var(--bp-border-color);
  box-shadow: var(--bp-card-shadow);
}

.bp5-menu-item:hover {
  background: rgba(33, 93, 176, 0.1);
  color: var(--bp-primary-blue);
}

/* Clean Loading Spinner */
.bp5-spinner .bp5-spinner-track {
  stroke: var(--bp-border-color);
}

.bp5-spinner .bp5-spinner-head {
  stroke: var(--bp-primary-blue);
}
