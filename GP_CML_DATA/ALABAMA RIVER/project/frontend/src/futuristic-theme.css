/* Futuristic Theme - Inspired by Tesla/SpaceX Design */

/* Global Variables - Using Official Blueprint.js v5 Colors */
:root {
  --bp-primary-blue: #215db0;
  --bp-secondary-blue: #2d72d2;
  --bp-accent-blue: #48aff0;
  --bp-dark-text: #1c2127;
  --bp-darker-bg: #1c2127;
  --bp-card-bg: #252a31;
  --bp-text-primary: #f5f8fa;
  --bp-text-secondary: #5f6b7c;
  --bp-text-muted: #8f99a8;
  --bp-text-disabled: #abb3bf;
  --bp-success-green: #1c6e42;
  --bp-warning-orange: #935610;
  --bp-error-red: #ac2f33;
  --bp-light-bg: #f6f7f9;
  --bp-border-color: rgba(33, 93, 176, 0.2);
  --bp-glow-shadow: 0 0 20px rgba(33, 93, 176, 0.3);
  --bp-subtle-glow: 0 0 10px rgba(33, 93, 176, 0.1);
}

/* Futuristic Navbar */
.futuristic-navbar {
  position: relative;
  overflow: hidden;
}

.futuristic-navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(33, 93, 176, 0.1), transparent);
  animation: scan 3s infinite;
}

@keyframes scan {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Enhanced Progress Bars */
.bp5-progress-bar .bp5-progress-meter {
  background: linear-gradient(90deg, var(--primary-blue), var(--accent-cyan));
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  border-radius: 5px;
}

.bp5-progress-bar.bp5-intent-success .bp5-progress-meter {
  background: linear-gradient(90deg, var(--success-green), #4ade80);
  box-shadow: 0 0 10px rgba(61, 204, 145, 0.5);
}

.bp5-progress-bar.bp5-intent-warning .bp5-progress-meter {
  background: linear-gradient(90deg, var(--warning-orange), #fbbf24);
  box-shadow: 0 0 10px rgba(255, 179, 102, 0.5);
}

/* Futuristic Cards */
.futuristic-card {
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--darker-bg) 100%);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: var(--subtle-glow), 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.futuristic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
  opacity: 0.6;
}

.futuristic-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--glow-shadow), 0 8px 24px rgba(0, 0, 0, 0.4);
  border-color: var(--primary-blue);
}

/* Enhanced Table Styling */
.futuristic-table {
  background: var(--darker-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--subtle-glow), 0 4px 12px rgba(0, 0, 0, 0.3);
}

.futuristic-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--darker-bg) 100%);
  border-bottom: 2px solid var(--border-color);
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  padding: 12px 8px;
}

.futuristic-table .ant-table-tbody > tr > td {
  background: var(--darker-bg);
  border-bottom: 1px solid rgba(0, 212, 255, 0.1);
  color: var(--text-primary);
  font-size: 13px;
  padding: 8px;
  transition: all 0.2s ease;
}

.futuristic-table .ant-table-tbody > tr:hover > td {
  background: rgba(0, 212, 255, 0.05);
  box-shadow: inset 0 0 0 1px rgba(0, 212, 255, 0.2);
}

/* Component Type Row Colors - Enhanced */
.head-row {
  background: linear-gradient(90deg, rgba(138, 187, 255, 0.15), rgba(138, 187, 255, 0.08)) !important;
  border-left: 3px solid #8abbff;
}

.head-row:hover > td {
  background: linear-gradient(90deg, rgba(138, 187, 255, 0.25), rgba(138, 187, 255, 0.15)) !important;
  box-shadow: inset 0 0 0 1px rgba(138, 187, 255, 0.3);
}

.roof-row {
  background: linear-gradient(90deg, rgba(114, 202, 155, 0.15), rgba(114, 202, 155, 0.08)) !important;
  border-left: 3px solid #72ca9b;
}

.roof-row:hover > td {
  background: linear-gradient(90deg, rgba(114, 202, 155, 0.25), rgba(114, 202, 155, 0.15)) !important;
  box-shadow: inset 0 0 0 1px rgba(114, 202, 155, 0.3);
}

.bottom-row {
  background: linear-gradient(90deg, rgba(251, 179, 96, 0.15), rgba(251, 179, 96, 0.08)) !important;
  border-left: 3px solid #fbb360;
}

.bottom-row:hover > td {
  background: linear-gradient(90deg, rgba(251, 179, 96, 0.25), rgba(251, 179, 96, 0.15)) !important;
  box-shadow: inset 0 0 0 1px rgba(251, 179, 96, 0.3);
}

/* Enhanced Asset Cards */
.asset-card {
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--darker-bg) 100%);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.asset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--text-muted);
  transition: all 0.3s ease;
}

.asset-card:hover {
  transform: translateX(4px);
  box-shadow: var(--subtle-glow), 0 4px 12px rgba(0, 0, 0, 0.3);
  border-color: var(--primary-blue);
}

.asset-card:hover::before {
  background: var(--primary-blue);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.asset-card.selected {
  border-color: var(--primary-blue);
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
  box-shadow: var(--subtle-glow);
}

.asset-card.selected::before {
  background: var(--primary-blue);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.asset-card.ready::before {
  background: var(--success-green);
}

.asset-card.not-ready::before {
  background: var(--warning-orange);
}

/* Enhanced Tabs */
.bp5-tab-list {
  background: var(--darker-bg);
  border-bottom: 1px solid var(--border-color);
}

.bp5-tab {
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
}

.bp5-tab[aria-selected="true"] {
  color: var(--primary-blue);
  box-shadow: inset 0 -2px 0 var(--primary-blue);
}

.bp5-tab:hover {
  color: var(--text-primary);
}

/* Enhanced Buttons */
.bp5-button.futuristic-btn {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  border: none;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
}

.bp5-button.futuristic-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 212, 255, 0.4);
}

/* Enhanced Checkboxes */
.custom-checkbox .ant-checkbox-inner {
  background: var(--darker-bg);
  border: 2px solid var(--border-color);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.custom-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  border-color: var(--primary-blue);
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--darker-bg);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--accent-cyan), var(--primary-blue));
}

/* Glow Effects */
.glow-text {
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.glow-border {
  box-shadow: 0 0 0 1px var(--primary-blue), var(--subtle-glow);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Additional Tesla/SpaceX inspired enhancements */
.bp5-navbar.futuristic-navbar {
  position: relative;
  background: linear-gradient(135deg, #1c2127 0%, #252a31 50%, #1c2127 100%);
  border-bottom: 2px solid rgba(0, 212, 255, 0.3);
}

.bp5-navbar.futuristic-navbar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: glow-line 2s ease-in-out infinite alternate;
}

@keyframes glow-line {
  0% { opacity: 0.3; }
  100% { opacity: 1; }
}

/* Enhanced button hover effects */
.bp5-button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Futuristic input styling */
.bp5-input {
  background: rgba(28, 33, 39, 0.8);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #f5f8fa;
  transition: all 0.3s ease;
}

.bp5-input:focus {
  border-color: #00d4ff;
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2), 0 0 10px rgba(0, 212, 255, 0.3);
}

/* Enhanced dropdown styling */
.bp5-menu {
  background: linear-gradient(135deg, #252a31 0%, #1c2127 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 212, 255, 0.1);
}

.bp5-menu-item:hover {
  background: rgba(0, 212, 255, 0.1);
  color: #00d4ff;
}

/* Loading spinner enhancement */
.bp5-spinner .bp5-spinner-track {
  stroke: rgba(0, 212, 255, 0.3);
}

.bp5-spinner .bp5-spinner-head {
  stroke: #00d4ff;
  filter: drop-shadow(0 0 4px rgba(0, 212, 255, 0.5));
}
