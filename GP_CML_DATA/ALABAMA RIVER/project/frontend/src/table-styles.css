/* Fix for table display */
.compact-table .ant-table-container {
  overflow-x: auto;
  border-width: 1px;
}

.compact-table .ant-table-body {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

.compact-table .ant-table-cell {
  padding: 8px;
  /* Allow content to wrap by default */
  white-space: normal;
  border: 1px solid rgba(0, 212, 255, 0.1);
  font-size: 13px;
  color: #f5f8fa;
  background: #1c2127;
  transition: all 0.2s ease;
}

.compact-table .ant-table-tbody > tr > td {
  background: #1c2127 !important;
  border-bottom: 1px solid rgba(0, 212, 255, 0.1) !important;
  color: #f5f8fa !important;
  font-size: 13px !important;
  transition: all 0.2s ease !important;
}

.compact-table .ant-table-tbody > tr:hover > td {
  background: rgba(0, 212, 255, 0.05) !important;
  box-shadow: inset 0 0 0 1px rgba(0, 212, 255, 0.2) !important;
}

/* Make sure the table uses all available width */
.compact-table {
  width: 100%;
}

/* Ensure columns have appropriate minimum width */
.compact-table th {
  min-width: 80px;
}

/* Special handling for Component Location and CML Description columns */
.compact-table td[class*="Component Location"],
.compact-table td[class*="CML Description"] {
  white-space: normal;
  word-break: break-word;
  min-width: 150px;
}

/* Fix for the Component Tag Name column which is often cut off */
.compact-table th:last-child,
.compact-table td:last-child {
  min-width: 150px;
}

/* Enhanced Blueprint v5 themed row styling for different component types */

/* HEAD and HX, HEAD rows - Enhanced with gradients and borders */
.head-row {
  background: linear-gradient(90deg, rgba(138, 187, 255, 0.15), rgba(138, 187, 255, 0.08)) !important;
  border-left: 3px solid #8abbff !important;
  position: relative;
}

.head-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #8abbff, #6ba3ff);
  box-shadow: 0 0 8px rgba(138, 187, 255, 0.4);
}

.head-row:hover > td {
  background: linear-gradient(90deg, rgba(138, 187, 255, 0.25), rgba(138, 187, 255, 0.15)) !important;
  box-shadow: inset 0 0 0 1px rgba(138, 187, 255, 0.3);
}

/* ROOF rows - Enhanced with gradients and borders */
.roof-row {
  background: linear-gradient(90deg, rgba(114, 202, 155, 0.15), rgba(114, 202, 155, 0.08)) !important;
  border-left: 3px solid #72ca9b !important;
  position: relative;
}

.roof-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #72ca9b, #5bb88a);
  box-shadow: 0 0 8px rgba(114, 202, 155, 0.4);
}

.roof-row:hover > td {
  background: linear-gradient(90deg, rgba(114, 202, 155, 0.25), rgba(114, 202, 155, 0.15)) !important;
  box-shadow: inset 0 0 0 1px rgba(114, 202, 155, 0.3);
}

/* BOTTOM rows - Enhanced with gradients and borders */
.bottom-row {
  background: linear-gradient(90deg, rgba(251, 179, 96, 0.15), rgba(251, 179, 96, 0.08)) !important;
  border-left: 3px solid #fbb360 !important;
  position: relative;
}

.bottom-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #fbb360, #f9a94e);
  box-shadow: 0 0 8px rgba(251, 179, 96, 0.4);
}

.bottom-row:hover > td {
  background: linear-gradient(90deg, rgba(251, 179, 96, 0.25), rgba(251, 179, 96, 0.15)) !important;
  box-shadow: inset 0 0 0 1px rgba(251, 179, 96, 0.3);
}

/* SHELL rows - Enhanced with gradients and borders */
.shell-row {
  background: linear-gradient(90deg, rgba(143, 153, 168, 0.15), rgba(143, 153, 168, 0.08)) !important;
  border-left: 3px solid #8f99a8 !important;
  position: relative;
}

.shell-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #8f99a8, #7a8496);
  box-shadow: 0 0 8px rgba(143, 153, 168, 0.4);
}

.shell-row:hover > td {
  background: linear-gradient(90deg, rgba(143, 153, 168, 0.25), rgba(143, 153, 168, 0.15)) !important;
  box-shadow: inset 0 0 0 1px rgba(143, 153, 168, 0.3);
}

/* Styles for the table container to ensure proper scrolling */
.compact-table {
  max-height: calc(100vh - 300px);
  overflow: auto;
}

.compact-table .ant-table-container {
  border-radius: 0;
}

/* Enhanced table header with futuristic styling */
.compact-table .ant-table-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background: linear-gradient(135deg, #252a31 0%, #1c2127 100%);
  border-bottom: 2px solid rgba(0, 212, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.compact-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #252a31 0%, #1c2127 100%) !important;
  border-bottom: 2px solid rgba(0, 212, 255, 0.2) !important;
  color: #f5f8fa !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 12px !important;
  padding: 12px 8px !important;
  position: relative;
}

.compact-table .ant-table-thead > tr > th::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.5), transparent);
}

/* Ensure the fixed columns work properly with scrolling */
.compact-table .ant-table-cell.ant-table-cell-fix-left,
.compact-table .ant-table-cell.ant-table-cell-fix-right {
  z-index: 3;
  background-color: #1c2127;
}

/* Style for resize handle */
.resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: col-resize;
  background-color: transparent;
}

.resize-handle:hover {
  background-color: rgba(138, 155, 168, 0.3);
}

/* Ensure table cells have proper padding and alignment */
.compact-table .ant-table-cell {
  padding: 6px 8px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  border-width: 1px !important;
}

/* Auto-size table layout */
.compact-table .ant-table {
  table-layout: auto;
  width: auto;
  border-width: 1px;
  font-size: 13px;
}

.compact-table .ant-table-thead > tr > th,
.compact-table .ant-table-tbody > tr > td {
  border-width: 1px !important;
}

/* Ensure the scrollbar is always visible for better UX */
.compact-table::-webkit-scrollbar,
.sidebar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.compact-table::-webkit-scrollbar-track,
.sidebar::-webkit-scrollbar-track {
  background: #252a31;
}

.compact-table::-webkit-scrollbar-thumb,
.sidebar::-webkit-scrollbar-thumb {
  background-color: #5c7080;
  border-radius: 4px;
}

.compact-table::-webkit-scrollbar-thumb:hover,
.sidebar::-webkit-scrollbar-thumb:hover {
  background-color: #738694;
}

/* Improved asset card styling */
.asset-card {
  margin-bottom: 8px;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.asset-card:hover {
  transform: translateX(2px);
  box-shadow: 0 0 0 1px rgba(16, 22, 26, 0.5), 0 2px 4px rgba(16, 22, 26, 0.5), 0 8px 24px rgba(16, 22, 26, 0.5) !important;
}

.asset-card.selected {
  border-left-color: #48aff0;
  background-color: rgba(19, 124, 189, 0.2) !important;
}

.asset-card.ready {
  border-left-color: #3dcc91;
}

.asset-card.not-ready {
  border-left-color: #ffb366;
}

/* Filter button styling */
.filter-button {
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s ease;
}

.filter-button:hover {
  transform: translateY(-1px);
}

/* Styling for table filter dropdowns */
.compact-table .ant-table-filter-trigger {
  color: #bfccd6;
  margin-right: 0;
}

.compact-table .ant-table-filter-trigger:hover {
  color: #ffffff;
  background-color: rgba(138, 155, 168, 0.2);
}

.compact-table .ant-table-filter-trigger.active {
  color: #48aff0;
  background-color: rgba(19, 124, 189, 0.2);
}

/* Style for filter dropdown menu */
.ant-dropdown .ant-table-filter-dropdown {
  background-color: #252a31;
  border: 1px solid #394b59;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
}

.ant-dropdown .ant-table-filter-dropdown-btns {
  border-top: 1px solid #394b59;
}

.ant-dropdown .ant-table-filter-dropdown-search input {
  background-color: #1C2127;
  color: #f5f8fa;
  border-color: #394b59;
  font-size: 13px;
}

.ant-dropdown .ant-table-filter-dropdown-search input:focus {
  border-color: #48aff0;
}

.ant-dropdown .ant-checkbox-wrapper {
  color: #f5f8fa;
}

.ant-dropdown .ant-table-filter-dropdown-btns .ant-btn {
  background-color: #1c2127;
  color: #f5f8fa;
  border-color: #394b59;
}

.ant-dropdown .ant-table-filter-dropdown-btns .ant-btn-primary {
  background-color: #137cbd;
  border-color: #137cbd;
}
