import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Classes, FocusStyleManager } from '@blueprintjs/core';
import Navigation from './components/Navigation';
import AssetManagement from './components/AssetManagement';
import AssetExplorer from './components/AssetExplorer';
import './App.css';
import './futuristic-theme.css';

// Enable focus indicators for keyboard navigation
FocusStyleManager.onlyShowFocusOnTabs();

function App() {

  return (
    <Router>
      <div 
        className="bp5-dark" 
        style={{ height: '100vh', overflow: 'hidden', backgroundColor: '#1c2127' }}
      >
        <Navigation />
        <div style={{ 
          marginLeft: '60px', // Space for the sidebar
          height: '100%',
          overflow: 'hidden'
        }}>
          <Routes>
            <Route path="/" element={<AssetManagement />} />
            <Route path="/explorer" element={<AssetExplorer />} />
          </Routes>
        </div>
      </div>
    </Router>
  );
}

export default App;
